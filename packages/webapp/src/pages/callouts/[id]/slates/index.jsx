import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import Head from 'next/head';
import { setCachedRoute } from 'reducer/auth';
import Loader from 'sharedComponents/loader';
import { getCallOut, getCalloutSubmissions } from 'reducer/callout';
import { useRouter } from 'next/router';
import SlateList from './slatesList';
import Button from 'sharedComponents/Button/button';
import NewSidebar from 'sharedComponents/NewSidebar/newsidebar';
import { withSubscriptionBanner } from 'sharedComponents/Subscription';
import { slateSectionConfig } from 'utils/helper';

const Index = (props) => {
  const {
    isLoading,
    userData,
    // token,
    getCalloutSubmissions,
    calloutSubmissions,
  } = props;
  const router = useRouter();

  const [slatesList, setSlatesList] = useState(false);
  const [renderSection, setRenderSection] = useState([
    'awaiting',
    'interested',
    'rejected',
  ]);
  const [slateView, setSlateView] = useState(true);
  const [slateCategory, setSlateCategory] = useState([]);
  const [visibleSection, setVisibleSection] = useState(null); // Track which section is visible

  useEffect(() => {
    const { setCachedRoute, token } = props;
    const { id } = router.query;

    const fetchData = async () => {
      if (!token) {
        setCachedRoute(`/callouts/${id}/slates`);
      } else {
        setCachedRoute('');
        if (id) {
          await getCallOut(id);
          await getCalloutSubmissions({
            calloutId: id,
            type: 'slate',
            sort: 'updatedAt|-1',
          });
        }
      }
    };

    fetchData();
  }, [props.token, router.query.id]);

  useEffect(() => {
    if (!Array.isArray(calloutSubmissions.docs)) return;

    const slateCategoryList = [];

    Object.entries(slateSectionConfig).forEach(([key, config]) => {
      slateCategoryList.push({
        id: key,
        heading: config.heading,
        noProjectsMsg: config.noProjectsMsg,
        data: [],
      });
    });

    // Create a lookup by status
    const slateSection = {};
    slateCategoryList.forEach((section) => {
      const config = slateSectionConfig[section.id];
      config.allowedStatus.forEach((status) => {
        slateSection[status] = section;
      });
    });

    // Assign slates to their category
    for (const slate of calloutSubmissions.docs || []) {
      const section = slateSection[slate.status];
      if (section) {
        section.data.push(slate);
      }
    }

    setSlateCategory(slateCategoryList);
  }, [calloutSubmissions]);

  const goToCallOuts = () => {
    const { id } = router.query;
    router.push(`/callouts/${id}`);
  };

  const handleSetSlatesList = (section) => {
    setSlatesList((prevStatus) => !prevStatus);
    const slateDisplay = section.length === 3 ? true : false;

    setSlateView(slateDisplay);
    setRenderSection(section);
  };

  const handleViewAll = (sectionId) => {
    setVisibleSection(sectionId);
  };

  const handleBackToAll = () => {
    setVisibleSection(null);
  };

  // Filter sections based on visibility
  const sectionsToRender = visibleSection
    ? slateCategory.filter((section) => section.id === visibleSection)
    : slateCategory;

  return (
    <div className="themeContainer">
      <Head>
        <title>Projects | Smash</title>
        <link rel="shortcut icon" href="/favicon.ico" />
      </Head>

      <div className="container-fluid">
        <div className="row">
          <div className="col-12 col-md-2 col-lg-2 p-0">
            <NewSidebar profile={userData.profile} />
          </div>
          <div
            className="col-12 col-md-10 col-lg-10"
            style={{ backgroundColor: '#F7F7F3', minHeight: '100vh' }}
          >
            {isLoading ? (
              <Loader />
            ) : (
              <div className="container">
                {!slatesList && (
                  <>
                    <div className="row justify-content-center mt-5 px-3">
                      <div className="col-12 col-md-3">
                        {visibleSection && (
                          <Button
                            btntype="button"
                            customClass="waitListBtn"
                            className="py-2 px-3"
                            clickHandler={handleBackToAll}
                            buttonValue="← Back to All"
                          />
                        )}
                      </div>
                      <div className="col-12 col-md-6 text-center mt-5 mt-md-0 mt-lg-0">
                        <h1 className="text-primary m-0  fs-18 fs-md-24 fs-lg-24">
                          Call out slate
                        </h1>
                      </div>
                      <div className="col-12 col-md-3 d-none d-md-block text-md-right text-center ">
                        <Button
                          btntype="submit"
                          customClass="waitListBtn"
                          className="w-100 py-2 px-28 py-md-1 px-md-3"
                          clickHandler={goToCallOuts}
                          buttonValue="View call out"
                        />
                      </div>
                    </div>

                    <p className="text-center text-primary mt-3 fs-14">
                      New opportunities for your projects
                    </p>
                    <div className="col-12 col-md-2 d-block d-md-none d-flex justify-content-center align-items-center p-0">
                      <Button
                        btntype="submit"
                        customClass="waitListBtn"
                        className="d-block w-100 text-center fs-12"
                        clickHandler={goToCallOuts}
                        buttonValue="View call out"
                      />
                    </div>
                  </>
                )}

                {sectionsToRender.map((section, index) => (
                  <>
                    {renderSection.includes(section.id) && (
                      <div className="row" key={index}>
                        <div className="col-12">
                          <SlateList
                            sectionId={section.id}
                            sectionHeading={section.heading}
                            noProjectsMsg={section.noProjectsMsg}
                            setSlateList={handleSetSlatesList}
                            isDashboardView={slateView}
                            calloutSubmissions={section.data}
                            calloutId={router.query.id}
                            showViewAll={!visibleSection} // Show "View All" button only when all sections are visible
                            onViewAll={() => handleViewAll(section.id)}
                            isExpandedView={visibleSection === section.id} // Flag to indicate if this section is in expanded view
                          />
                        </div>
                      </div>
                    )}
                  </>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

Index.propTypes = {
  isLoading: PropTypes.bool.isRequired,
  getCallOut: PropTypes.func.isRequired,
  userData: PropTypes.object.isRequired,
};

const mapStateToProps = (state) => ({
  isLoading: state.project.isLoading,
  userData: state.auth.userData,
  token: state.auth.token,
  calloutSubmissions: state.callout.calloutSubmissions,
});

const mapDispatchToProps = (dispatch) => ({
  getCallOut: (id) => dispatch(getCallOut(id)),
  setCachedRoute: (route) => dispatch(setCachedRoute(route)),
  getCalloutSubmissions: (options) => dispatch(getCalloutSubmissions(options)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withSubscriptionBanner(Index));
