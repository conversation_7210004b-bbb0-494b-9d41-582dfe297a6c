/* eslint-disable no-console */
const { get } = require('lodash');
const { Server } = require('socket.io');
const AuthService = require('../src/services/AuthService');

const Socket = (function () {
  let io;

  /**
   * Handle socket authentication using handshake method
   *
   * @returns {void} - void
   */
  const handleHandShake = async function () {
    await io.use(async (socket, next) => {
      const token = get(socket, 'handshake.auth.token', false);
      const user = await AuthService.validateUserAuthToken(token);
      if (user) {
        socket.user = user;
        next();
      } else {
        next(new Error('Invalid token'));
      }
    });
  };

  /**
   * Handle socket connection/disconnection
   *
   * @returns {void} - void
   */
  const handleConnection = async function () {
    await io.on('connection', (socket) => {
      socket.on('room', (data) => {
        if (!(socket.rooms && socket.rooms.has(data.roomName))) {
          socket.join(data.roomName);
          global.logger().info(`Socket join room: ${data.roomName}`);
        }
      });

      /* Handle disconnection of socket */
      socket.on('disconnect', () => {
        console.log(`User:${get(socket.user, '_id')} socket disconnected!`);
        console.info(`Socket disconnected fun() called!`);
      });

      /* Handle socket error */
      socket.on('error', (error) => {
        console.error(error, `Socket getting error!`);
      });
    });
  };

  return {
    /**
     * This method return Socket instance
     *
     * @returns {instance} Socket instance
     */
    get: function () {
      return io;
    },

    /**
     * This method create and return Socket instance
     *
     * @param {any} listener - hapi js server listener
     * @returns {void}
     */
    init: async function (listener) {
      try {
        io = await new Server(listener, {
          cors: {
            origin: process.env.SOCKET_ALLOWED_ORIGIN,
          },
        });
        console.info(`Socket initialize successfully`);

        /* Handle authentication */
        await handleHandShake();

        /* Handle connections */
        await handleConnection();

        return io;
      } catch (error) {
        console.error(error, `Socket getting error while initialize!`);
      }
    },
  };
})();

module.exports = Socket;
