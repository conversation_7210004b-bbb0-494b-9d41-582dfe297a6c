const BaseService = require('./BaseService');
const CollaboratorSchema = require('../models/collaboratorInvite');

class CollaboratorService extends BaseService {
  /**
   * This method responsible to create collaborator.
   *
   * @param {String} payload collaborator id
   */
  async create(payload) {
    global.logger().info('CollaboratorService.create() called!');
    const result = await this.save('CollaboratorInvite', payload);
    global.logger().info('Collaborator created!');
    return result;
  }

  /**
   * This method responsible to update collaborator data.
   *
   * @param {String} id -  collaborator id
   * @param {Object} payload collaborator data object
   */
  async patch(where, data) {
    global.logger().info('CollaboratorService.update() called!');
    const result = await this.update('CollaboratorInvite', where, data, {
      upsert: true,
    });
    global.logger().info('Collaborator data updated!');
    return result;
  }

  /**
   * This method is responsible for get collaborator list.
   *
   * @param {*} where - mongoose condition
   * @param {*} option - mongoose option
   * @return {Tags obj} - result tags data
   */
  async get(where, options = {}) {
    global.logger().info('CollaboratorService.get() method called!');
    const result = await this.find('CollaboratorInvite', where, options);
    global.logger().info('Get collaborator list successfully!');
    return result;
  }

  async findProjectCollaborator(collabId) {
    try {
      const mongoose = require('mongoose');

      const objectId = new mongoose.Types.ObjectId(collabId);
      return await CollaboratorSchema.aggregate([
        {
          $match: { _id: objectId },
        },
        {
          $lookup: {
            from: 'projects',
            localField: 'projectInfo.projectsId',
            foreignField: '_id',
            as: 'projects',
          },
        },
        {
          $project: {
            email: 1,
            matchingCollaborators: {
              $reduce: {
                input: '$projects',
                initialValue: [],
                in: {
                  $concatArrays: [
                    '$$value',
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$$this.projectCollaborator',
                            as: 'collab',
                            cond: { $eq: ['$$collab.email', '$email'] },
                          },
                        },
                        as: 'matched',
                        in: {
                          $mergeObjects: [
                            '$$matched',
                            { projectId: '$$this._id' },
                          ],
                        },
                      },
                    },
                  ],
                },
              },
            },
          },
        },
      ]);
    } catch (err) {
      return err;
    }
  }

  /**
   * This method responsible to get collaborator data.
   *
   * @param {*} where - mongoose condition
   * @param {*} options - mongoose option
   * @returns {*} collaborator data
   */
  async getOne(where, options = {}) {
    global.logger().info('CollaboratorService.getOne() called!');
    const result = await this.findOne('CollaboratorInvite', where, options);
    global.logger().info('Get collaborator data successfully!');
    return result;
  }

  /**
   * This method is responsible for get collaborator list.
   *
   * @param {*} query - mongoose condition
   * @param {*} options - mongoose option
   * @return {Tags obj} - result tags data
   */
  async pagination(query, options) {
    global.logger().info('CollaboratorService.get() method called!');
    const { Model } = this.getModel('CollaboratorInvite', 'Collaborator');
    const result = await Model.paginate(query, options);
    global.logger().info('Get collaborator list successfully!');
    return result;
  }

  /**
   * This method responsible to delete collaborator data.
   *
   * @param {*} where - mongoose condition
   * @param {*} options - mongoose option
   * @returns {*} collaborator data
   */
  async remove(where, options = {}) {
    global.logger().info('CollaboratorService.remove() called!');
    const result = await this.delete('CollaboratorInvite', where, options);
    global.logger().info('Remove collaborator data successfully!');
    return result;
  }
}

module.exports = new CollaboratorService();
