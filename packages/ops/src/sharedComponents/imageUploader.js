import React, { useState } from 'react';
import { ImageInput } from 'react-admin';
import { CircularProgress, Typography } from '@mui/material';
import Storage from '../lib/storage';

const ImageUpload = ({
  source,
  label,
  maxSize,
  onUploadSuccess,
  defaultValue,
  userId,
}) => {
  const [loading, setLoading] = useState(false);
  const [fileName, setFileName] = useState(defaultValue);

  const fileInputHandler = async (file) => {
    let token = Storage.get('token').replace(/"/g, '');

    if (!file || !token) {
      return false;
    }

    setLoading(true);
    setFileName(file.name);

    try {
      // Generate presigned URL
      const response = await fetch(
        `${process.env.REACT_APP_API_BASE_URL}/v1/upload/generatePresignedUrl`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({ filename: file.name }),
        }
      );

      const result = await response.json();
      const signedUrl = result?.data?.signedUrl || '';

      if (!signedUrl) return false;

      // Upload file to signed URL
      await fetch(signedUrl, {
        method: 'PUT',
        body: file,
        headers: { 'Content-Type': file.type },
      });

      const publicUrl = result?.data?.publicUrl || null;
      setLoading(false);
      if (onUploadSuccess) onUploadSuccess(publicUrl);
    } catch (error) {
      console.error('Upload Error:', error);
      setLoading(false);
    }
  };

  return (
    <ImageInput
      accept="image/*"
      source={source}
      label={label || ' '}
      maxSize={maxSize || 10000000}
      style={{
        border: '1px solid #ced4da',
        width: '100%',
        boxSizing: 'border-box',
        padding: '8px',
      }}
      placeholder={
        <Typography
          variant="subtitle2"
          color="text.primary"
          className="fw-500 text-left"
        >
          {loading ? (
            <CircularProgress size={24} />
          ) : fileName ? (
            fileName
          ) : (
            <>
              No file chosen.{' '}
              <span
                style={{
                  color: '#007bff',
                  textDecoration: 'underline',
                  cursor: 'pointer',
                }}
              >
                Browse files
              </span>{' '}
              or drag and drop
            </>
          )}
        </Typography>
      }
      className="h-100"
      onChange={fileInputHandler}
    />
  );
};

export default ImageUpload;
