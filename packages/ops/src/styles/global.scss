@import 'node_modules/bootstrap/scss/bootstrap.scss';

@font-face {
  font-family: 'robotoRegular';
  src: url('../assets/fonts/roboto/Regular.ttf') format('truetype');
}

/* @font-face {
  font-family: 'PettingillCFBold';
  src: url('../assets/fonts/PettingillCF/PettingillCFBold.otf')
    format('truetype');
} */

@font-face {
  font-family: 'robotoMedium';
  src: url('../assets/fonts/roboto/RobotoMedium.ttf') format('truetype');
}

@font-face {
  font-family: 'chaney';
  src: url('../assets/fonts/Chaney/CHANEY-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'maisonNeue';
  src: url('../assets/fonts/maisonNeue/MaisonNeue.ttf') format('truetype');
}

@font-face {
  font-family: 'MaisonNeueMono';
  src: url('../assets/fonts/maisonNeue/MaisonNeueMono.otf') format('truetype');
}

@font-face {
  font-family: 'robotoCondensed';
  src: url('../assets/fonts/robotoCondensed/RobotoCondensedRegular.ttf')
    format('truetype');
}

@font-face {
  font-family: 'Inter';
  src: url('../assets/fonts/Inter/Inter-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Inter-bold';
  src: url('../assets/fonts/Inter/Inter-Bold.ttf') format('truetype');
}

.admin-btn {
  background-color: #00a3ff !important;
  color: #ffffff;
}

.opsActions {
  min-width: 0px !important;
  background-color: #ffffff !important;
}

.badge-btn {
  font-family: 'Helvetica';
}

.admin-link {
  color: #00a3ff;
  cursor: pointer;
}

.admin-btn-link {
  color: #00a3ff;
  background: transparent;
  border: none;
  outline: none;
  text-align: left;
  &:hover {
    color: #0a1f44;
    text-decoration: underline;
  }
}

.admin-dark {
  color: #0a1f44;
}

.badge-pill {
  font-size: 12px;
  font-style: normal;
  display: flex;
  padding: 3px 8px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 40px;
  background: #05012d;
  color: #ecece0;
  font-family: 'Helvetica';
}

.badge-primary {
  color: #ffffff;
  background-color: #05012d;
}
.badge-secondary {
  color: #212529;
  background-color: #ecece0;
}
.badge-success {
  color: #212529;
  background-color: #00e5d5;
}

.badge-warning {
  color: #212529;
  background-color: #ebff29;
}

.badge-danger {
  color: #ffffff;
  background-color: #ff303d;
}

.badge-dark {
  color: #ffffff;
  background-color: #5a5a5a;
}

.badge-info {
  color: #212529;
  background-color: rgba(25, 118, 210, 0.08);
}

.css-umly8t-MuiTypography-root {
  text-align: left !important;
}

#editor-root {
  margin: 0 auto;
  max-width: 800px;
  padding: 0 1rem;
  font-family: 'maisonNeue';
  font-style: normal;
  font-weight: normal;
  div {
    font-family: 'maisonNeue';
    font-style: normal;
    font-weight: normal;
    font-size: 18px;
  }
}
.ck-powered-by-balloon {
  display: none !important;
}

.smash-editor-title {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  margin-bottom: 1.25rem !important;
  text-align: center !important;
  p {
    font-size: 1.5rem;
    font-weight: 800;
  }
}

.smash-editor-company-name {
  text-align: center !important;
  p {
    font-size: 1.25rem;
    font-weight: 800;
  }
}

.smash-editor-logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2.5rem;
}

.modalBtn {
  background-color: #05012d;
  border: 2px solid #05012d !important;
  box-shadow: 0 0 1px 0 rgba(10, 22, 70, 0.06),
    0 1px 1px 0 rgba(10, 22, 70, 0.1);
  color: #fff !important;
  font-family: 'chaney' !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-size: 16px !important;
  line-height: 30px !important;
  text-align: center !important;
  padding: 7px !important;

  @media screen and (max-width: 370px) {
    font-size: 12px !important;
  }
}

.disabledBtn {
  background-color: #e1e4e8 !important;
  color: #05012d !important;
  font-family: 'chaney' !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-size: 16px !important;
  line-height: 30px !important;
  text-align: center !important;
  padding: 11px 15px !important;
  cursor: not-allowed;
  border: 2px solid #05012d;

  @media screen and (max-width: 370px) {
    font-size: 12px !important;
  }
}

.textInput {
  box-sizing: border-box;
  height: 42px;
  border-radius: 0 !important;
  border: 2px solid #05012d;
  background-color: #ffffff !important;
  color: #05012d;
  font-size: 16px !important;
  letter-spacing: 0;
  line-height: 19px;
  display: block;
  width: 100%;
  font-family: 'maisonNeue' !important;
  padding: 8px 16px !important;
}

.textLabelFilled {
  padding-top: 10px;
  box-sizing: border-box;
  height: 40px;
  border-radius: 0 !important;
  border: 0px;
  background-color: #eee !important;
  color: #05012d !important;
  font-size: 14px;
  letter-spacing: 0;
  line-height: 19px;
  display: block;
  width: 100%;
  font-family: 'maisonNeue' !important;
  padding: 8px 16px !important;
}

.errorClass {
  box-shadow: 0 0 1px 0 rgba(10, 22, 70, 0.06),
    0 6px 6px -1px rgba(10, 22, 70, 0.1);
  background: #ffefed !important;
  border-color: #f1998e !important;
}

.textInput:focus-visible {
  outline: 0px !important;
}

.textInput::placeholder {
  color: #414141 !important;
}

.paragraph {
  font-family: 'maisonNeue';
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 21px;
  color: #05012d !important;
}

p {
  margin: 0px;
  padding: 0px;
}

.heading {
  font-family: 'chaney';
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 22px;
  color: #05012d !important;
}

.modal-container {
  padding: 32px 16px;
  max-width: 497px;
  max-height: 690px;
  margin: 0 auto;
  background-color: #ffffff;
}

.bg {
  position: absolute;

  /* Preserve aspet ratio */
  min-width: 100%;
  min-height: 100%;
}

.fluid-container {
  width: auto;
}

.backGroundImage {
  background-image: url('../../public/assets/png/smash-back-01.png');
  /* Center and scale the image nicely */
  background-repeat: no-repeat;
  background-size: cover;
}

.dashboardHeader {
  border-bottom: 1px;
  border-bottom-style: solid;
  border-color: #000000 !important;
  height: 60px;
  background-color: #ffffff !important;
  top: 0;
  padding: 6px 16px !important;
  height: 62px !important;
}

.smallText {
  font-family: 'maisonNeue';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
}

.link-btn {
  padding-left: 0;
  font-weight: 500;
  color: #1743d7;
  background: transparent;
  border: none;
}

p {
  margin: 0 !important;
  padding: 0 !important;
}

.tag-item {
  margin-right: 8px;
}

.column-name {
  min-width: 360px !important;
}

.column-projectName {
  min-width: 260px !important;
}

.column-projectSnapshot {
  min-width: 180px !important;
}

.column-undefined {
  font-weight: 700 !important;
}

.RaFilterForm-filterFormInput {
  width: 100% !important;
}

.column-title {
  min-width: 300px !important;
}

.column-projectName {
  min-width: 300px !important;
}

.MuiLink-underlineAlways {
  text-decoration: none !important;
}

.RaFilterFormInput-spacer {
  display: none !important;
}

.MuiDialogActions-spacing {
  padding: 10px 24px !important;
}